@echo off
title VolvoFlashWR - Integrated VCRedist and Architecture Bridge Fix
echo ===============================================
echo VolvoFlashWR - Integrated Startup
echo ===============================================
echo.

echo Setting up VolvoFlashWR environment with integrated fixes...
echo - Visual C++ Redistributable auto-download
echo - Architecture bridge for x86/x64 compatibility
echo - Enhanced library loading
echo.

REM Add current directory to PATH for immediate library access
set PATH=%CD%;%CD%\Libraries;%CD%\Libraries\VCRedist;%CD%\Bridge;%PATH%

REM Set integrated environment variables
set FORCE_ARCHITECTURE_BRIDGE=true
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set APCI_LIBRARY_PATH=%CD%\Libraries
set VCREDIST_AUTO_DOWNLOAD=true
set INTEGRATED_STARTUP=true

echo Environment configured for integrated compatibility
echo.

REM Check if bridge executable exists
if exist "Bridge\VolvoFlashWR.VocomBridge.exe" (
    echo ✓ Architecture bridge available
) else (
    echo ⚠ Architecture bridge not found - may have compatibility issues
)

REM Check for critical libraries
if exist "Libraries\apci.dll" (
    echo ✓ APCI library found
) else (
    echo ⚠ APCI library not found
)

echo.
echo Starting VolvoFlashWR with integrated fixes...
echo The application will automatically:
echo - Download missing Visual C++ libraries if needed
echo - Use architecture bridge for x86/x64 compatibility
echo - Handle library loading issues automatically
echo.

REM Try to run the application
if exist "VolvoFlashWR.Launcher.exe" (
    echo Starting VolvoFlashWR.Launcher.exe...
    "VolvoFlashWR.Launcher.exe"
) else if exist "VolvoFlashWR.UI.exe" (
    echo Starting VolvoFlashWR.UI.exe...
    "VolvoFlashWR.UI.exe"
) else (
    echo ERROR: No VolvoFlashWR executable found!
    echo.
    echo Expected files:
    echo - VolvoFlashWR.Launcher.exe
    echo - VolvoFlashWR.UI.exe
    echo.
    pause
    exit /b 1
)

REM Check exit code and provide helpful information
if errorlevel 1 (
    echo.
    echo ===============================================
    echo Application exited with error code: %errorlevel%
    echo ===============================================
    echo.
    echo Check the Logs folder for detailed error information.
    echo.
    echo If you still experience issues:
    echo 1. Check the latest log file in the Logs folder
    echo 2. Ensure you have internet connection for library downloads
    echo 3. Run as Administrator if permission issues occur
    echo 4. Check Windows Update for system updates
    echo.
    echo The integrated system should handle most compatibility issues automatically.
    echo.
    pause
) else (
    echo.
    echo Application started successfully!
)
