using System;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom.ArchitectureBridge
{
    /// <summary>
    /// Factory that creates the appropriate Vocom service based on architecture compatibility
    /// Automatically handles the x64/x86 mismatch by choosing the right implementation
    /// </summary>
    public class ArchitectureAwareVocomServiceFactory
    {
        private readonly ILoggingService _logger;

        public ArchitectureAwareVocomServiceFactory(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates the most appropriate Vocom service based on current architecture and library availability
        /// </summary>
        public IVocomService CreateVocomService()
        {
            try
            {
                _logger.LogInformation("=== Architecture-Aware Vocom Service Factory ===", "ArchitectureAwareVocomServiceFactory");
                
                // Check current process architecture
                bool is64BitProcess = Environment.Is64BitProcess;
                string processArch = is64BitProcess ? "x64" : "x86";
                _logger.LogInformation($"Current process architecture: {processArch}", "ArchitectureAwareVocomServiceFactory");

                // Check library architecture compatibility
                var compatibilityResult = CheckLibraryCompatibility();
                _logger.LogInformation($"Library compatibility check: {compatibilityResult}", "ArchitectureAwareVocomServiceFactory");

                // Decide which service to create based on compatibility
                if (compatibilityResult == LibraryCompatibility.Compatible)
                {
                    _logger.LogInformation("Libraries are compatible - using direct Vocom service", "ArchitectureAwareVocomServiceFactory");
                    return CreateDirectVocomService();
                }
                else if (compatibilityResult == LibraryCompatibility.ArchitectureMismatch)
                {
                    _logger.LogInformation("Architecture mismatch detected - implementing enhanced compatibility strategy", "ArchitectureAwareVocomServiceFactory");

                    // Check if we can use the architecture bridge
                    bool bridgeAvailable = CheckArchitectureBridgeAvailability();

                    if (bridgeAvailable)
                    {
                        _logger.LogInformation("Architecture bridge available - creating bridged service for x86 library compatibility", "ArchitectureAwareVocomServiceFactory");
                        return CreateBridgedVocomService();
                    }
                    else
                    {
                        _logger.LogWarning("Architecture bridge not available - attempting compatibility workarounds", "ArchitectureAwareVocomServiceFactory");

                        // Try to create a compatibility service that handles architecture mismatch gracefully
                        var compatibilityService = CreateCompatibilityVocomService();
                        if (compatibilityService != null)
                        {
                            _logger.LogInformation("Compatibility service created successfully", "ArchitectureAwareVocomServiceFactory");
                            return compatibilityService;
                        }

                        _logger.LogWarning("All compatibility options exhausted - falling back to dummy service", "ArchitectureAwareVocomServiceFactory");
                        return CreateDummyVocomService();
                    }
                }
                else
                {
                    _logger.LogWarning("Libraries missing or incompatible - trying direct detection before falling back to dummy service", "ArchitectureAwareVocomServiceFactory");

                    // Even with missing libraries, try direct detection first
                    var directService = TryCreateDirectVocomService();
                    if (directService != null)
                    {
                        _logger.LogInformation("Direct service worked despite missing libraries - using direct service", "ArchitectureAwareVocomServiceFactory");
                        return directService;
                    }

                    return CreateDummyVocomService();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception in service factory: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                _logger.LogWarning("Falling back to dummy Vocom service due to exception", "ArchitectureAwareVocomServiceFactory");
                return CreateDummyVocomService();
            }
        }

        /// <summary>
        /// Checks the compatibility of Phoenix APCI libraries with current process architecture
        /// </summary>
        private LibraryCompatibility CheckLibraryCompatibility()
        {
            try
            {
                bool is64BitProcess = Environment.Is64BitProcess;
                var appPath = AppDomain.CurrentDomain.BaseDirectory;
                var librariesPath = Path.Combine(appPath, "Libraries");

                // Critical libraries to check
                string[] criticalLibraries = {
                    "apci.dll",
                    "Volvo.ApciPlus.dll",
                    "WUDFPuma.dll"
                };

                int compatibleCount = 0;
                int incompatibleCount = 0;
                int missingCount = 0;

                foreach (string library in criticalLibraries)
                {
                    string libraryPath = Path.Combine(librariesPath, library);
                    
                    if (!File.Exists(libraryPath))
                    {
                        _logger.LogWarning($"Critical library missing: {library}", "ArchitectureAwareVocomServiceFactory");
                        missingCount++;
                        continue;
                    }

                    var archCompatibility = CheckLibraryArchitecture(libraryPath, is64BitProcess);
                    if (archCompatibility)
                    {
                        compatibleCount++;
                        _logger.LogInformation($"✓ Compatible library: {library}", "ArchitectureAwareVocomServiceFactory");
                    }
                    else
                    {
                        incompatibleCount++;
                        _logger.LogWarning($"✗ Architecture mismatch: {library}", "ArchitectureAwareVocomServiceFactory");
                    }
                }

                // Determine overall compatibility
                if (missingCount > 0)
                {
                    _logger.LogWarning($"Missing {missingCount} critical libraries", "ArchitectureAwareVocomServiceFactory");
                    return LibraryCompatibility.Missing;
                }

                if (incompatibleCount > 0)
                {
                    _logger.LogWarning($"Found {incompatibleCount} incompatible libraries", "ArchitectureAwareVocomServiceFactory");
                    return LibraryCompatibility.ArchitectureMismatch;
                }

                if (compatibleCount == criticalLibraries.Length)
                {
                    _logger.LogInformation("All critical libraries are compatible", "ArchitectureAwareVocomServiceFactory");
                    return LibraryCompatibility.Compatible;
                }

                return LibraryCompatibility.Unknown;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during compatibility check: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                return LibraryCompatibility.Unknown;
            }
        }

        /// <summary>
        /// Checks if a library's architecture matches the current process architecture
        /// </summary>
        private bool CheckLibraryArchitecture(string libraryPath, bool is64BitProcess)
        {
            try
            {
                using var fileStream = new FileStream(libraryPath, FileMode.Open, FileAccess.Read);
                using var reader = new BinaryReader(fileStream);

                // Check DOS header
                if (reader.ReadUInt16() != 0x5A4D) // "MZ"
                    return false;

                // Jump to PE header
                fileStream.Seek(60, SeekOrigin.Begin);
                int peHeaderOffset = reader.ReadInt32();
                fileStream.Seek(peHeaderOffset, SeekOrigin.Begin);

                // Check PE signature
                if (reader.ReadUInt32() != 0x00004550) // "PE\0\0"
                    return false;

                // Read machine type
                ushort machineType = reader.ReadUInt16();

                // Determine compatibility
                bool is64BitLibrary = (machineType == 0x8664); // IMAGE_FILE_MACHINE_AMD64
                bool is32BitLibrary = (machineType == 0x014c); // IMAGE_FILE_MACHINE_I386

                return (is64BitProcess && is64BitLibrary) || (!is64BitProcess && is32BitLibrary);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error checking architecture for {libraryPath}: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                return false;
            }
        }

        /// <summary>
        /// Creates a direct Vocom service (for compatible architectures)
        /// </summary>
        private IVocomService CreateDirectVocomService()
        {
            try
            {
                // Use the existing patched Vocom service factory
                var patchedFactory = new PatchedVocomServiceFactory(_logger);
                var serviceTask = patchedFactory.CreateServiceAsync();
                serviceTask.Wait(); // Wait for the async operation to complete
                return serviceTask.Result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to create direct Vocom service: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                return CreateDummyVocomService();
            }
        }

        /// <summary>
        /// Tries to create a direct Vocom service even with architecture mismatch
        /// Returns null if it fails, allowing fallback to bridge
        /// </summary>
        private IVocomService? TryCreateDirectVocomService()
        {
            try
            {
                _logger.LogInformation("Attempting to create direct Vocom service to preserve original working detection", "ArchitectureAwareVocomServiceFactory");

                // Use the existing patched Vocom service factory
                var patchedFactory = new PatchedVocomServiceFactory(_logger);
                var serviceTask = patchedFactory.CreateServiceAsync();
                serviceTask.Wait(10000); // Wait up to 10 seconds for service creation

                var service = serviceTask.Result;
                if (service != null)
                {
                    // Test if the service can actually detect devices
                    _logger.LogInformation("Testing direct service device detection capability", "ArchitectureAwareVocomServiceFactory");

                    // Try to detect devices to verify the service is working
                    var detectTask = service.ScanForDevicesAsync();
                    detectTask.Wait(10000); // Wait up to 10 seconds for detection

                    if (detectTask.IsCompletedSuccessfully)
                    {
                        var devices = detectTask.Result;
                        _logger.LogInformation($"Direct service detected {devices?.Count ?? 0} total devices", "ArchitectureAwareVocomServiceFactory");

                        // Log all detected devices for debugging
                        if (devices != null && devices.Count > 0)
                        {
                            foreach (var device in devices)
                            {
                                _logger.LogInformation($"  - Device: {device.Name} (ID: {device.Id}, Type: {device.ConnectionType})", "ArchitectureAwareVocomServiceFactory");
                            }

                            // Check for real devices (not dummy/simulated)
                            var realDevices = devices.Where(d =>
                                !d.Name.Contains("Dummy", StringComparison.OrdinalIgnoreCase) &&
                                !d.Name.Contains("Simulated", StringComparison.OrdinalIgnoreCase) &&
                                !d.Name.Contains("Test", StringComparison.OrdinalIgnoreCase) &&
                                (d.Name.Contains("Vocom", StringComparison.OrdinalIgnoreCase) ||
                                 d.Name.Contains("88890300", StringComparison.OrdinalIgnoreCase) ||
                                 d.Name.Contains("88890020", StringComparison.OrdinalIgnoreCase) ||
                                 d.SerialNumber?.Contains("88890", StringComparison.OrdinalIgnoreCase) == true)
                            ).ToList();

                            if (realDevices.Count > 0)
                            {
                                _logger.LogInformation($"Direct service found {realDevices.Count} real Vocom devices", "ArchitectureAwareVocomServiceFactory");
                                foreach (var realDevice in realDevices)
                                {
                                    _logger.LogInformation($"  - Real device: {realDevice.Name} (Serial: {realDevice.SerialNumber})", "ArchitectureAwareVocomServiceFactory");
                                }

                                // Test actual communication capability, not just detection
                                _logger.LogInformation("Testing direct service communication capability with real devices", "ArchitectureAwareVocomServiceFactory");

                                try
                                {
                                    // Try to connect to the first real device to test communication
                                    var testDevice = realDevices.First();
                                    var connectTask = service.ConnectToDeviceAsync(testDevice.Id);
                                    connectTask.Wait(5000); // Wait up to 5 seconds for connection test

                                    if (connectTask.IsCompletedSuccessfully && connectTask.Result)
                                    {
                                        _logger.LogInformation("Direct service successfully tested communication - using direct service despite architecture mismatch", "ArchitectureAwareVocomServiceFactory");
                                        // Disconnect after test
                                        try { service.DisconnectAsync().Wait(2000); } catch { }
                                        return service;
                                    }
                                    else
                                    {
                                        _logger.LogWarning("Direct service failed communication test - architecture mismatch prevents actual communication", "ArchitectureAwareVocomServiceFactory");
                                    }
                                }
                                catch (Exception commEx)
                                {
                                    _logger.LogWarning($"Direct service communication test failed: {commEx.Message}", "ArchitectureAwareVocomServiceFactory");
                                }
                            }
                            else
                            {
                                _logger.LogInformation("Direct service found devices but none appear to be real Vocom adapters", "ArchitectureAwareVocomServiceFactory");
                            }
                        }
                        else
                        {
                            _logger.LogInformation("Direct service did not detect any devices", "ArchitectureAwareVocomServiceFactory");
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Direct service device detection timed out or failed", "ArchitectureAwareVocomServiceFactory");
                    }
                }
                else
                {
                    _logger.LogWarning("Failed to create direct Vocom service - service is null", "ArchitectureAwareVocomServiceFactory");
                }

                _logger.LogInformation("Direct service cannot communicate with real devices due to architecture mismatch - will fall back to bridge service", "ArchitectureAwareVocomServiceFactory");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Exception while trying to create direct Vocom service: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                _logger.LogWarning($"Stack trace: {ex.StackTrace}", "ArchitectureAwareVocomServiceFactory");
                _logger.LogInformation("Direct service failed with exception - will fall back to bridge service", "ArchitectureAwareVocomServiceFactory");
                return null;
            }
        }

        /// <summary>
        /// Creates a bridged Vocom service (for architecture mismatches)
        /// </summary>
        private IVocomService CreateBridgedVocomService()
        {
            try
            {
                _logger.LogInformation("Architecture mismatch detected - creating bridged Vocom service", "ArchitectureAwareVocomServiceFactory");

                // Create the bridged service that uses the architecture bridge
                var bridgedService = new BridgedVocomService(_logger);

                // Initialize the service asynchronously
                var initTask = bridgedService.InitializeAsync();
                initTask.Wait(); // Wait for initialization to complete

                if (initTask.Result)
                {
                    _logger.LogInformation("Bridged Vocom service created and initialized successfully", "ArchitectureAwareVocomServiceFactory");
                    return bridgedService;
                }
                else
                {
                    _logger.LogWarning("Bridged Vocom service initialization failed, falling back to dummy mode", "ArchitectureAwareVocomServiceFactory");
                    return CreateDummyVocomService();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to create bridged Vocom service: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                _logger.LogWarning("Falling back to dummy mode due to bridge service failure", "ArchitectureAwareVocomServiceFactory");
                return CreateDummyVocomService();
            }
        }

        /// <summary>
        /// Checks if the architecture bridge is available and functional
        /// </summary>
        private bool CheckArchitectureBridgeAvailability()
        {
            try
            {
                var appPath = AppDomain.CurrentDomain.BaseDirectory;
                var bridgePath = Path.Combine(appPath, "Bridge", "VolvoFlashWR.VocomBridge.exe");

                if (!File.Exists(bridgePath))
                {
                    _logger.LogWarning($"Architecture bridge not found at: {bridgePath}", "ArchitectureAwareVocomServiceFactory");
                    return false;
                }

                // Check if bridge is x86 architecture (required for APCI libraries)
                bool isBridgeX86 = CheckLibraryArchitecture(bridgePath, false); // false = check for x86
                if (!isBridgeX86)
                {
                    _logger.LogWarning("Architecture bridge is not x86 - cannot load x86 APCI libraries", "ArchitectureAwareVocomServiceFactory");
                    return false;
                }

                _logger.LogInformation("Architecture bridge is available and compatible", "ArchitectureAwareVocomServiceFactory");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error checking architecture bridge availability: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                return false;
            }
        }

        /// <summary>
        /// Creates a compatibility service that handles architecture mismatches gracefully
        /// </summary>
        private IVocomService? CreateCompatibilityVocomService()
        {
            try
            {
                _logger.LogInformation("Creating compatibility Vocom service for architecture mismatch handling", "ArchitectureAwareVocomServiceFactory");

                // Create a service that can handle architecture mismatches by using alternative detection methods
                var compatibilityService = new CompatibilityVocomService(_logger);

                // Test if the service can initialize
                var initTask = compatibilityService.InitializeAsync();
                initTask.Wait(TimeSpan.FromSeconds(10)); // 10 second timeout

                if (initTask.IsCompletedSuccessfully && initTask.Result)
                {
                    _logger.LogInformation("Compatibility Vocom service initialized successfully", "ArchitectureAwareVocomServiceFactory");
                    return compatibilityService;
                }
                else
                {
                    _logger.LogWarning("Compatibility Vocom service failed to initialize", "ArchitectureAwareVocomServiceFactory");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error creating compatibility Vocom service: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                return null;
            }
        }

        /// <summary>
        /// Creates a dummy Vocom service (fallback)
        /// </summary>
        private IVocomService CreateDummyVocomService()
        {
            try
            {
                return new DummyVocomService(_logger);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to create dummy Vocom service: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                throw new InvalidOperationException("Unable to create any Vocom service implementation", ex);
            }
        }
    }

    /// <summary>
    /// Enumeration of library compatibility states
    /// </summary>
    public enum LibraryCompatibility
    {
        Compatible,
        ArchitectureMismatch,
        Missing,
        Unknown
    }
}
